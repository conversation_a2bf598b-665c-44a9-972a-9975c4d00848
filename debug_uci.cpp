#include "include/UCIProtocol.h"
#include <iostream>
#include <fstream>

int main() {
    // Enable debug logging
    std::ofstream logFile("uci_debug.log");
    
    try {
        UCIProtocol uci;
        
        // Test basic UCI commands
        std::cout << "Testing UCI Protocol..." << std::endl;
        logFile << "Starting UCI test" << std::endl;
        
        // Simulate UCI commands
        std::vector<std::string> uciCmd = {"uci"};
        std::vector<std::string> readyCmd = {"isready"};
        std::vector<std::string> posCmd = {"position", "startpos", "moves", "e2e4", "e7e5"};
        std::vector<std::string> goCmd = {"go", "depth", "3"};
        
        std::cout << "=== UCI Command ===" << std::endl;
        logFile << "Sending UCI command" << std::endl;
        
        std::cout << "=== IsReady Command ===" << std::endl;
        logFile << "Sending isready command" << std::endl;
        
        std::cout << "=== Position Command ===" << std::endl;
        logFile << "Sending position command" << std::endl;
        
        std::cout << "=== Go Command ===" << std::endl;
        logFile << "Sending go command" << std::endl;
        
        // Test the engine directly
        ChessEngine engine(Color::WHITE, 3);
        ChessGame game;
        
        // Make some moves
        Move e2e4(Position(1, 4), Position(3, 4)); // e2-e4
        Move e7e5(Position(6, 4), Position(4, 4)); // e7-e5
        
        if (game.makeMove(e2e4)) {
            std::cout << "Move e2e4 successful" << std::endl;
            logFile << "Move e2e4 successful" << std::endl;
        }
        
        if (game.makeMove(e7e5)) {
            std::cout << "Move e7e5 successful" << std::endl;
            logFile << "Move e7e5 successful" << std::endl;
        }
        
        // Test engine search
        std::cout << "Testing engine search..." << std::endl;
        logFile << "Testing engine search" << std::endl;
        
        Move bestMove = engine.getBestMoveWithTime(game, 1000);
        std::cout << "Best move: " << bestMove.toAlgebraic() << std::endl;
        logFile << "Best move: " << bestMove.toAlgebraic() << std::endl;
        
        // Test evaluation
        int eval = engine.evaluatePosition(game.getBoard(), Color::WHITE);
        std::cout << "Evaluation: " << eval << std::endl;
        logFile << "Evaluation: " << eval << std::endl;
        
        std::cout << "Search stats: " << engine.getAnalysisString() << std::endl;
        logFile << "Search stats: " << engine.getAnalysisString() << std::endl;
        
        logFile.close();
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        logFile << "Error: " << e.what() << std::endl;
        logFile.close();
        return 1;
    }
}
