cmake_minimum_required(VERSION 3.10)
project(ChessEngine)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Add include directory
include_directories(include)

# Collect all source files
file(GLOB_RECURSE SOURCES "src/*.cpp")

# Create executable
add_executable(chess_engine main.cpp ${SOURCES})

# Set compiler flags for better debugging and warnings
if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
    target_compile_options(chess_engine PRIVATE -Wall -Wextra -O2)
endif()

if(MSVC)
    target_compile_options(chess_engine PRIVATE /W4 /O2)
endif()
