<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VibeChess Engine - Web Version</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background-color: #1a1a1a;
            color: #00ff00;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            border: 2px solid #00ff00;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .game-area {
            display: flex;
            gap: 20px;
        }
        
        .board-container {
            flex: 1;
        }
        
        .chess-board {
            display: grid;
            grid-template-columns: repeat(8, 60px);
            grid-template-rows: repeat(8, 60px);
            border: 2px solid #00ff00;
            margin: 20px 0;
        }
        
        .square {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            cursor: pointer;
            border: 1px solid #333;
        }
        
        .light {
            background-color: #f0d9b5;
            color: #000;
        }
        
        .dark {
            background-color: #b58863;
            color: #000;
        }
        
        .selected {
            background-color: #ffff00 !important;
        }
        
        .possible-move {
            background-color: #90EE90 !important;
        }
        
        .control-panel {
            flex: 1;
            border: 2px solid #00ff00;
            padding: 20px;
        }
        
        .engine-config {
            margin-bottom: 20px;
        }
        
        .config-item {
            margin: 10px 0;
        }
        
        .config-item label {
            display: inline-block;
            width: 120px;
        }
        
        .config-item input, .config-item select {
            background-color: #333;
            color: #00ff00;
            border: 1px solid #00ff00;
            padding: 5px;
        }
        
        .move-history {
            height: 200px;
            overflow-y: auto;
            border: 1px solid #00ff00;
            padding: 10px;
            margin: 10px 0;
            background-color: #333;
        }
        
        .engine-output {
            height: 150px;
            overflow-y: auto;
            border: 1px solid #00ff00;
            padding: 10px;
            margin: 10px 0;
            background-color: #333;
            font-size: 12px;
        }
        
        button {
            background-color: #333;
            color: #00ff00;
            border: 2px solid #00ff00;
            padding: 10px 20px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background-color: #00ff00;
            color: #000;
        }
        
        .status {
            padding: 10px;
            border: 1px solid #00ff00;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏆 VibeChess Engine - Web Version</h1>
            <p>Play against the VibeChess AI engine in your browser!</p>
        </div>
        
        <div class="game-area">
            <div class="board-container">
                <div class="status" id="gameStatus">
                    Game Status: Ready to play | Your turn (White)
                </div>
                
                <div class="chess-board" id="chessBoard">
                    <!-- Board will be generated by JavaScript -->
                </div>
                
                <div class="move-input">
                    <label>Manual Move: </label>
                    <input type="text" id="moveInput" placeholder="e2e4" maxlength="5">
                    <button onclick="makeManualMove()">Make Move</button>
                </div>
            </div>
            
            <div class="control-panel">
                <h3>🔧 Engine Configuration</h3>
                <div class="engine-config">
                    <div class="config-item">
                        <label>Difficulty:</label>
                        <select id="engineDepth" onchange="updateEngineConfig()">
                            <option value="2">Beginner (Depth 2)</option>
                            <option value="3">Easy (Depth 3)</option>
                            <option value="4" selected>Normal (Depth 4)</option>
                            <option value="5">Hard (Depth 5)</option>
                            <option value="6">Expert (Depth 6)</option>
                        </select>
                    </div>
                    
                    <div class="config-item">
                        <label>Think Time:</label>
                        <select id="thinkTime" onchange="updateEngineConfig()">
                            <option value="1000">1 second</option>
                            <option value="3000" selected>3 seconds</option>
                            <option value="5000">5 seconds</option>
                            <option value="10000">10 seconds</option>
                        </select>
                    </div>
                    
                    <div class="config-item">
                        <label>Your Color:</label>
                        <select id="playerColor" onchange="newGame()">
                            <option value="white" selected>White</option>
                            <option value="black">Black</option>
                        </select>
                    </div>
                </div>
                
                <div class="game-controls">
                    <button onclick="newGame()">🆕 New Game</button>
                    <button onclick="undoMove()">↶ Undo Move</button>
                    <button onclick="getHint()">💡 Hint</button>
                    <button onclick="analyzePosition()">🔍 Analyze</button>
                </div>
                
                <h4>📝 Move History</h4>
                <div class="move-history" id="moveHistory">
                    Game started. Make your first move!
                </div>
                
                <h4>🤖 Engine Output</h4>
                <div class="engine-output" id="engineOutput">
                    VibeChess Engine ready...<br>
                    Depth: 4, Time: 3s<br>
                    Waiting for your move...
                </div>
                
                <div class="status">
                    <strong>Engine Status:</strong> <span id="engineStatus">Ready</span><br>
                    <strong>Last Evaluation:</strong> <span id="lastEval">+0.00</span><br>
                    <strong>Nodes Searched:</strong> <span id="nodesSearched">0</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Chess engine web interface
        class ChessEngineWeb {
            constructor() {
                this.board = this.initializeBoard();
                this.currentPlayer = 'white';
                this.playerColor = 'white';
                this.engineDepth = 4;
                this.thinkTime = 3000;
                this.moveHistory = [];
                this.selectedSquare = null;
                this.gameOver = false;
                
                this.initializeUI();
                this.renderBoard();
            }
            
            initializeBoard() {
                // Standard chess starting position
                return [
                    ['♜','♞','♝','♛','♚','♝','♞','♜'],
                    ['♟','♟','♟','♟','♟','♟','♟','♟'],
                    ['','','','','','','',''],
                    ['','','','','','','',''],
                    ['','','','','','','',''],
                    ['','','','','','','',''],
                    ['♙','♙','♙','♙','♙','♙','♙','♙'],
                    ['♖','♘','♗','♕','♔','♗','♘','♖']
                ];
            }
            
            initializeUI() {
                const boardElement = document.getElementById('chessBoard');
                boardElement.innerHTML = '';
                
                for (let row = 0; row < 8; row++) {
                    for (let col = 0; col < 8; col++) {
                        const square = document.createElement('div');
                        square.className = `square ${(row + col) % 2 === 0 ? 'light' : 'dark'}`;
                        square.dataset.row = row;
                        square.dataset.col = col;
                        square.onclick = () => this.handleSquareClick(row, col);
                        boardElement.appendChild(square);
                    }
                }
            }
            
            renderBoard() {
                const squares = document.querySelectorAll('.square');
                squares.forEach((square, index) => {
                    const row = Math.floor(index / 8);
                    const col = index % 8;
                    square.textContent = this.board[row][col];
                    
                    // Remove highlighting
                    square.classList.remove('selected', 'possible-move');
                });
            }
            
            handleSquareClick(row, col) {
                if (this.gameOver) return;
                if (this.currentPlayer !== this.playerColor) return;
                
                const piece = this.board[row][col];
                
                if (this.selectedSquare) {
                    // Try to make a move
                    const fromRow = this.selectedSquare.row;
                    const fromCol = this.selectedSquare.col;
                    
                    if (this.isValidMove(fromRow, fromCol, row, col)) {
                        this.makeMove(fromRow, fromCol, row, col);
                        this.selectedSquare = null;
                        this.renderBoard();
                        
                        if (!this.gameOver) {
                            setTimeout(() => this.makeEngineMove(), 500);
                        }
                    } else {
                        this.selectedSquare = null;
                        this.renderBoard();
                    }
                } else if (piece && this.isPieceOwnedByPlayer(piece)) {
                    // Select piece
                    this.selectedSquare = { row, col };
                    this.highlightSquare(row, col);
                    this.showPossibleMoves(row, col);
                }
            }
            
            isPieceOwnedByPlayer(piece) {
                const whitePieces = '♔♕♖♗♘♙';
                const blackPieces = '♚♛♜♝♞♟';
                
                if (this.playerColor === 'white') {
                    return whitePieces.includes(piece);
                } else {
                    return blackPieces.includes(piece);
                }
            }
            
            highlightSquare(row, col) {
                const squares = document.querySelectorAll('.square');
                const index = row * 8 + col;
                squares[index].classList.add('selected');
            }
            
            showPossibleMoves(row, col) {
                // Simplified move generation - in real implementation, 
                // this would use proper chess rules
                const moves = this.generatePossibleMoves(row, col);
                moves.forEach(move => {
                    const squares = document.querySelectorAll('.square');
                    const index = move.row * 8 + move.col;
                    squares[index].classList.add('possible-move');
                });
            }
            
            generatePossibleMoves(row, col) {
                // Simplified - just return adjacent squares for demo
                const moves = [];
                for (let r = Math.max(0, row-1); r <= Math.min(7, row+1); r++) {
                    for (let c = Math.max(0, col-1); c <= Math.min(7, col+1); c++) {
                        if (r !== row || c !== col) {
                            moves.push({row: r, col: c});
                        }
                    }
                }
                return moves;
            }
            
            isValidMove(fromRow, fromCol, toRow, toCol) {
                // Simplified validation - in real implementation,
                // this would check proper chess rules
                return toRow >= 0 && toRow < 8 && toCol >= 0 && toCol < 8;
            }
            
            makeMove(fromRow, fromCol, toRow, toCol) {
                const piece = this.board[fromRow][fromCol];
                const capturedPiece = this.board[toRow][toCol];
                
                this.board[toRow][toCol] = piece;
                this.board[fromRow][fromCol] = '';
                
                const moveNotation = this.getMoveNotation(fromRow, fromCol, toRow, toCol, piece, capturedPiece);
                this.moveHistory.push(moveNotation);
                this.updateMoveHistory();
                
                this.currentPlayer = this.currentPlayer === 'white' ? 'black' : 'white';
                this.updateGameStatus();
            }
            
            getMoveNotation(fromRow, fromCol, toRow, toCol, piece, captured) {
                const files = 'abcdefgh';
                const ranks = '87654321';
                
                const from = files[fromCol] + ranks[fromRow];
                const to = files[toCol] + ranks[toRow];
                
                return `${from}${to}${captured ? ' (captures ' + captured + ')' : ''}`;
            }
            
            makeEngineMove() {
                this.updateEngineStatus('Thinking...');
                this.addEngineOutput('Engine is analyzing position...');
                
                // Simulate engine thinking
                setTimeout(() => {
                    // Simple random move for demo - in real implementation,
                    // this would call the actual chess engine
                    const engineMove = this.generateEngineMove();
                    
                    if (engineMove) {
                        this.makeMove(engineMove.fromRow, engineMove.fromCol, 
                                    engineMove.toRow, engineMove.toCol);
                        this.renderBoard();
                        
                        // Simulate engine output
                        this.addEngineOutput(`Best move: ${this.getMoveNotation(
                            engineMove.fromRow, engineMove.fromCol,
                            engineMove.toRow, engineMove.toCol,
                            this.board[engineMove.toRow][engineMove.toCol], ''
                        )}`);
                        this.addEngineOutput(`Evaluation: +0.25`);
                        this.addEngineOutput(`Nodes searched: 15,432`);
                        
                        this.updateEngineStatus('Ready');
                        document.getElementById('lastEval').textContent = '+0.25';
                        document.getElementById('nodesSearched').textContent = '15,432';
                    }
                }, this.thinkTime);
            }
            
            generateEngineMove() {
                // Find all possible moves for engine
                const moves = [];
                for (let row = 0; row < 8; row++) {
                    for (let col = 0; col < 8; col++) {
                        const piece = this.board[row][col];
                        if (piece && !this.isPieceOwnedByPlayer(piece)) {
                            const possibleMoves = this.generatePossibleMoves(row, col);
                            possibleMoves.forEach(move => {
                                if (this.isValidMove(row, col, move.row, move.col)) {
                                    moves.push({
                                        fromRow: row, fromCol: col,
                                        toRow: move.row, toCol: move.col
                                    });
                                }
                            });
                        }
                    }
                }
                
                // Return random move for demo
                return moves.length > 0 ? moves[Math.floor(Math.random() * moves.length)] : null;
            }
            
            updateMoveHistory() {
                const historyElement = document.getElementById('moveHistory');
                historyElement.innerHTML = this.moveHistory.map((move, index) => 
                    `${Math.floor(index/2) + 1}. ${move}`
                ).join('<br>');
                historyElement.scrollTop = historyElement.scrollHeight;
            }
            
            updateGameStatus() {
                const statusElement = document.getElementById('gameStatus');
                const currentPlayerName = this.currentPlayer === this.playerColor ? 'Your' : 'Engine\'s';
                statusElement.textContent = `Game Status: Playing | ${currentPlayerName} turn (${this.currentPlayer})`;
            }
            
            updateEngineStatus(status) {
                document.getElementById('engineStatus').textContent = status;
            }
            
            addEngineOutput(text) {
                const outputElement = document.getElementById('engineOutput');
                outputElement.innerHTML += text + '<br>';
                outputElement.scrollTop = outputElement.scrollHeight;
            }
            
            newGame() {
                this.board = this.initializeBoard();
                this.currentPlayer = 'white';
                this.playerColor = document.getElementById('playerColor').value;
                this.moveHistory = [];
                this.selectedSquare = null;
                this.gameOver = false;
                
                this.renderBoard();
                this.updateMoveHistory();
                this.updateGameStatus();
                this.updateEngineStatus('Ready');
                
                document.getElementById('moveHistory').innerHTML = 'New game started!';
                document.getElementById('engineOutput').innerHTML = 'VibeChess Engine ready...<br>';
                
                if (this.playerColor === 'black') {
                    setTimeout(() => this.makeEngineMove(), 1000);
                }
            }
            
            undoMove() {
                if (this.moveHistory.length >= 2) {
                    // Undo last two moves (player + engine)
                    this.moveHistory.pop();
                    this.moveHistory.pop();
                    // In real implementation, would restore board state
                    this.addEngineOutput('Undo not fully implemented in demo');
                }
            }
            
            getHint() {
                this.addEngineOutput('Analyzing for best move...');
                setTimeout(() => {
                    this.addEngineOutput('Hint: Consider developing your pieces');
                    this.addEngineOutput('Suggested move: e2e4 (King\'s pawn opening)');
                }, 1000);
            }
            
            analyzePosition() {
                this.addEngineOutput('Deep analysis started...');
                setTimeout(() => {
                    this.addEngineOutput('Position evaluation: +0.15 (slightly better for white)');
                    this.addEngineOutput('Best line: e2e4 e7e5 Ng1f3 Nb8c6');
                    this.addEngineOutput('Key features: Central control, piece development');
                }, 2000);
            }
        }
        
        // Global functions
        function updateEngineConfig() {
            const depth = document.getElementById('engineDepth').value;
            const time = document.getElementById('thinkTime').value;
            
            game.engineDepth = parseInt(depth);
            game.thinkTime = parseInt(time);
            
            game.addEngineOutput(`Configuration updated: Depth=${depth}, Time=${time}ms`);
        }
        
        function makeManualMove() {
            const moveInput = document.getElementById('moveInput');
            const move = moveInput.value.toLowerCase();
            
            if (move.length === 4 || move.length === 5) {
                // Parse move like "e2e4"
                const fromFile = move.charCodeAt(0) - 97; // a=0, b=1, etc.
                const fromRank = 8 - parseInt(move[1]);   // 8=0, 7=1, etc.
                const toFile = move.charCodeAt(2) - 97;
                const toRank = 8 - parseInt(move[3]);
                
                if (game.isValidMove(fromRank, fromFile, toRank, toFile)) {
                    game.makeMove(fromRank, fromFile, toRank, toFile);
                    game.renderBoard();
                    moveInput.value = '';
                    
                    if (!game.gameOver) {
                        setTimeout(() => game.makeEngineMove(), 500);
                    }
                } else {
                    game.addEngineOutput('Invalid move: ' + move);
                }
            } else {
                game.addEngineOutput('Invalid move format. Use format like: e2e4');
            }
        }
        
        // Initialize game when page loads
        let game;
        window.onload = function() {
            game = new ChessEngineWeb();
        };
    </script>
</body>
</html>
