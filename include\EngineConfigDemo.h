#pragma once

#include "EngineConfig.h"
#include <string>
#include <vector>

// Demo class untuk menampilkan interface seperti di gambar
class EngineConfigDemo
{
private:
    EngineConfig &config_;

    // Display helpers
    void clearScreen() const;
    void displayConfigPanel() const;
    void displayParameterBox(const std::string &label, const std::string &value,
                             int x, int y, int width = 20, bool hasSpinner = true) const;
    void displayFileBox(const std::string &label, const std::string &value,
                        int x, int y, int width = 40) const;

    // Console positioning (simplified)
    void gotoxy(int x, int y) const;
    void setColor(int color) const;
    void resetColor() const;

    // Input handling
    std::string getInputAtPosition(int x, int y, int width) const;
    int getIntInputAtPosition(int x, int y, int width, int min, int max) const;

public:
    explicit EngineConfigDemo(EngineConfig &config);
    ~EngineConfigDemo() = default;

    // Main demo interface
    void showConfigInterface();
    void runInteractiveDemo();
    void handleMenuChoice(int choice);

    // Individual parameter displays
    void showNUMAOffset();
    void showThreads();
    void showHash();
    void showTableMemory();
    void showMultiPV();
    void showBookFile();
    void showBookMoves();
    void showHashFileName();
    void showLogFile();
    void showOverhead();
    void showMinimalReporting();
    void showTimeUsage();
    void showSyzygyPath();
    void showSyzygyProbeDepth();
};
