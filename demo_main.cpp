#include "include/EngineConfig.h"
#include "include/EngineConfigDemo.h"
#include <iostream>

int main() {
    std::cout << "=== VibeChess Engine Configuration Demo ===\n\n";
    
    try {
        // Create engine configuration
        EngineConfig config;
        
        // Create and run demo
        EngineConfigDemo demo(config);
        demo.runInteractiveDemo();
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
