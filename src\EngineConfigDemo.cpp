#include "EngineConfigDemo.h"
#include <iostream>
#include <iomanip>
#include <sstream>

#ifdef _WIN32
#include <windows.h>
#include <conio.h>
#else
#include <termios.h>
#include <unistd.h>
#endif

EngineConfigDemo::EngineConfigDemo(EngineConfig& config) : config_(config) {}

void EngineConfigDemo::showConfigInterface() {
    clearScreen();
    
    // Header
    std::cout << "┌─────────────────────────────────────────────────────────────────────────────┐\n";
    std::cout << "│                        VIBECHESS ENGINE CONFIGURATION                       │\n";
    std::cout << "└─────────────────────────────────────────────────────────────────────────────┘\n\n";
    
    // Left column parameters
    std::cout << "┌─────────────────────────────────┐  ┌─────────────────────────────────┐\n";
    std::cout << "│ NUMA Offset                     │  │ Threads                         │\n";
    std::cout << "│ " << std::setw(31) << std::left << config_.getParameter("NUMA Offset") << " │  │ " << std::setw(31) << std::left << config_.getParameter("Threads") << " │\n";
    std::cout << "│ ▲▼                              │  │ ▲▼                              │\n";
    std::cout << "└─────────────────────────────────┘  └─────────────────────────────────┘\n\n";
    
    std::cout << "┌─────────────────────────────────┐  ┌─────────────────────────────────┐\n";
    std::cout << "│ Hash                            │  │ Table Memory                    │\n";
    std::cout << "│ " << std::setw(31) << std::left << config_.getParameter("Hash") << " │  │ " << std::setw(31) << std::left << config_.getParameter("Table Memory") << " │\n";
    std::cout << "│ ▲▼                              │  │ ▲▼                              │\n";
    std::cout << "└─────────────────────────────────┘  └─────────────────────────────────┘\n\n";
    
    std::cout << "┌─────────────────────────────────┐  ┌─────────────────────────────────┐\n";
    std::cout << "│ MultiPV                         │  │ Book File                       │\n";
    std::cout << "│ " << std::setw(31) << std::left << config_.getParameter("MultiPV") << " │  │ " << std::setw(28) << std::left << config_.getParameter("Book File").substr(0, 28) << "... │\n";
    std::cout << "│ ▲▼                              │  │ [Browse...]                     │\n";
    std::cout << "└─────────────────────────────────┘  └─────────────────────────────────┘\n\n";
    
    std::cout << "┌─────────────────────────────────┐  ┌─────────────────────────────────┐\n";
    std::cout << "│ Book Moves                      │  │ Log File                        │\n";
    std::cout << "│ " << std::setw(31) << std::left << config_.getParameter("Book Moves") << " │  │ " << std::setw(28) << std::left << config_.getParameter("Log File").substr(0, 28) << "... │\n";
    std::cout << "│ ▲▼                              │  │ [Browse...]                     │\n";
    std::cout << "└─────────────────────────────────┘  └─────────────────────────────────┘\n\n";
    
    std::cout << "┌─────────────────────────────────┐  ┌─────────────────────────────────┐\n";
    std::cout << "│ Hash File Name                  │  │ Minimal Reporting               │\n";
    std::cout << "│ " << std::setw(28) << std::left << config_.getParameter("Hash File Name").substr(0, 28) << "... │  │ " << std::setw(31) << std::left << config_.getParameter("Minimal Reporting") << " │\n";
    std::cout << "│ [Browse...]                     │  │ ▲▼                              │\n";
    std::cout << "└─────────────────────────────────┘  └─────────────────────────────────┘\n\n";
    
    std::cout << "┌─────────────────────────────────┐  ┌─────────────────────────────────┐\n";
    std::cout << "│ Overhead ms                     │  │ Time Usage                      │\n";
    std::cout << "│ " << std::setw(31) << std::left << config_.getParameter("Overhead ms") << " │  │ " << std::setw(31) << std::left << config_.getParameter("Time Usage") << " │\n";
    std::cout << "│ ▲▼                              │  │ ▲▼                              │\n";
    std::cout << "└─────────────────────────────────┘  └─────────────────────────────────┘\n\n";
    
    std::cout << "┌─────────────────────────────────┐  ┌─────────────────────────────────┐\n";
    std::cout << "│ SyzygyPath                      │  │ Syzygy Probe Depth              │\n";
    std::cout << "│ " << std::setw(28) << std::left << config_.getParameter("SyzygyPath").substr(0, 28) << "... │  │ " << std::setw(31) << std::left << config_.getParameter("Syzygy Probe Depth") << " │\n";
    std::cout << "│ [Browse...]                     │  │ ▲▼                              │\n";
    std::cout << "└─────────────────────────────────┘  └─────────────────────────────────┘\n\n";
    
    std::cout << "Tekan Enter untuk menu interaktif, atau 'q' untuk keluar: ";
}

void EngineConfigDemo::runInteractiveDemo() {
    while (true) {
        showConfigInterface();
        
        std::string input;
        std::getline(std::cin, input);
        
        if (input == "q" || input == "quit") {
            break;
        }
        
        // Show interactive menu
        clearScreen();
        std::cout << "=== MENU KONFIGURASI INTERAKTIF ===\n\n";
        std::cout << "1. Edit NUMA Offset (" << config_.getParameter("NUMA Offset") << ")\n";
        std::cout << "2. Edit Threads (" << config_.getParameter("Threads") << ")\n";
        std::cout << "3. Edit Hash (" << config_.getParameter("Hash") << ")\n";
        std::cout << "4. Edit Table Memory (" << config_.getParameter("Table Memory") << ")\n";
        std::cout << "5. Edit MultiPV (" << config_.getParameter("MultiPV") << ")\n";
        std::cout << "6. Edit Book File (" << config_.getParameter("Book File") << ")\n";
        std::cout << "7. Edit Book Moves (" << config_.getParameter("Book Moves") << ")\n";
        std::cout << "8. Edit Log File (" << config_.getParameter("Log File") << ")\n";
        std::cout << "9. Edit Overhead ms (" << config_.getParameter("Overhead ms") << ")\n";
        std::cout << "10. Edit Minimal Reporting (" << config_.getParameter("Minimal Reporting") << ")\n";
        std::cout << "11. Edit Time Usage (" << config_.getParameter("Time Usage") << ")\n";
        std::cout << "12. Edit SyzygyPath (" << config_.getParameter("SyzygyPath") << ")\n";
        std::cout << "13. Edit Syzygy Probe Depth (" << config_.getParameter("Syzygy Probe Depth") << ")\n";
        std::cout << "s. Simpan konfigurasi\n";
        std::cout << "l. Muat konfigurasi\n";
        std::cout << "r. Reset ke default\n";
        std::cout << "b. Kembali ke tampilan utama\n";
        std::cout << "q. Keluar\n\n";
        std::cout << "Pilih opsi: ";
        
        std::getline(std::cin, input);
        
        if (input == "q") {
            break;
        } else if (input == "b") {
            continue;
        } else if (input == "s") {
            std::cout << "Nama file (default: engine_config.ini): ";
            std::string filename;
            std::getline(std::cin, filename);
            if (filename.empty()) filename = "engine_config.ini";
            
            if (config_.saveToFile(filename)) {
                std::cout << "Konfigurasi disimpan ke: " << filename << "\n";
            } else {
                std::cout << "Gagal menyimpan konfigurasi!\n";
            }
            std::cout << "Tekan Enter untuk melanjutkan...";
            std::cin.ignore();
        } else if (input == "l") {
            std::cout << "Nama file: ";
            std::string filename;
            std::getline(std::cin, filename);
            
            if (config_.loadFromFile(filename)) {
                std::cout << "Konfigurasi dimuat dari: " << filename << "\n";
            } else {
                std::cout << "Gagal memuat konfigurasi!\n";
            }
            std::cout << "Tekan Enter untuk melanjutkan...";
            std::cin.ignore();
        } else if (input == "r") {
            config_.resetToDefaults();
            std::cout << "Konfigurasi direset ke default.\n";
            std::cout << "Tekan Enter untuk melanjutkan...";
            std::cin.ignore();
        } else {
            try {
                int choice = std::stoi(input);
                handleMenuChoice(choice);
            } catch (const std::exception&) {
                std::cout << "Pilihan tidak valid!\n";
                std::cout << "Tekan Enter untuk melanjutkan...";
                std::cin.ignore();
            }
        }
    }
}

void EngineConfigDemo::handleMenuChoice(int choice) {
    std::string paramName;
    std::string prompt;
    int min = 0, max = 1000;
    bool isInteger = true;
    
    switch (choice) {
        case 1:
            paramName = "NUMA Offset";
            prompt = "NUMA Offset (0-7): ";
            min = 0; max = 7;
            break;
        case 2:
            paramName = "Threads";
            prompt = "Jumlah Threads (1-128): ";
            min = 1; max = 128;
            break;
        case 3:
            paramName = "Hash";
            prompt = "Hash Table Size MB (1-32768): ";
            min = 1; max = 32768;
            break;
        case 4:
            paramName = "Table Memory";
            prompt = "Table Memory MB (1-8192): ";
            min = 1; max = 8192;
            break;
        case 5:
            paramName = "MultiPV";
            prompt = "MultiPV (1-500): ";
            min = 1; max = 500;
            break;
        case 6:
            paramName = "Book File";
            prompt = "Book File Path: ";
            isInteger = false;
            break;
        case 7:
            paramName = "Book Moves";
            prompt = "Book Moves (0-10000): ";
            min = 0; max = 10000;
            break;
        case 8:
            paramName = "Log File";
            prompt = "Log File Path: ";
            isInteger = false;
            break;
        case 9:
            paramName = "Overhead ms";
            prompt = "Overhead ms (0-5000): ";
            min = 0; max = 5000;
            break;
        case 10:
            paramName = "Minimal Reporting";
            prompt = "Minimal Reporting ms (0-10000): ";
            min = 0; max = 10000;
            break;
        case 11:
            paramName = "Time Usage";
            prompt = "Time Usage % (0-100): ";
            min = 0; max = 100;
            break;
        case 12:
            paramName = "SyzygyPath";
            prompt = "Syzygy Path: ";
            isInteger = false;
            break;
        case 13:
            paramName = "Syzygy Probe Depth";
            prompt = "Syzygy Probe Depth (1-100): ";
            min = 1; max = 100;
            break;
        default:
            std::cout << "Pilihan tidak valid!\n";
            std::cout << "Tekan Enter untuk melanjutkan...";
            std::cin.ignore();
            return;
    }
    
    std::cout << "\nEdit " << paramName << "\n";
    std::cout << "Nilai saat ini: " << config_.getParameter(paramName) << "\n";
    std::cout << prompt;
    
    std::string newValue;
    std::getline(std::cin, newValue);
    
    if (!newValue.empty()) {
        if (isInteger) {
            try {
                int intValue = std::stoi(newValue);
                if (intValue >= min && intValue <= max) {
                    config_.setParameter(paramName, newValue);
                    std::cout << "Parameter berhasil diubah!\n";
                } else {
                    std::cout << "Nilai harus antara " << min << " dan " << max << "!\n";
                }
            } catch (const std::exception&) {
                std::cout << "Nilai tidak valid!\n";
            }
        } else {
            config_.setParameter(paramName, newValue);
            std::cout << "Parameter berhasil diubah!\n";
        }
    }
    
    std::cout << "Tekan Enter untuk melanjutkan...";
    std::cin.ignore();
}

void EngineConfigDemo::clearScreen() const {
#ifdef _WIN32
    system("cls");
#else
    system("clear");
#endif
}
