#include "include/EngineConfig.h"
#include "include/EngineConfigUI.h"
#include <iostream>
#include <string>

int main(int argc, char* argv[]) {
    std::cout << "=== VibeChess Engine Configuration Tool ===\n\n";
    
    // Create engine configuration
    EngineConfig config;
    
    // Check command line arguments
    if (argc > 1) {
        std::string arg = argv[1];
        
        if (arg == "--help" || arg == "-h") {
            std::cout << "Penggunaan:\n";
            std::cout << "  " << argv[0] << "                    - Jalankan interface konfigurasi\n";
            std::cout << "  " << argv[0] << " --load <file>      - Muat konfigurasi dari file\n";
            std::cout << "  " << argv[0] << " --save <file>      - Simpan konfigurasi ke file\n";
            std::cout << "  " << argv[0] << " --show             - <PERSON><PERSON><PERSON>an konfigurasi saat ini\n";
            std::cout << "  " << argv[0] << " --uci              - Export ke format UCI\n";
            std::cout << "  " << argv[0] << " --reset            - Reset ke pengaturan default\n";
            std::cout << "  " << argv[0] << " --help             - Tampilkan bantuan ini\n";
            return 0;
        }
        else if (arg == "--load" && argc > 2) {
            std::string filename = argv[2];
            if (config.loadFromFile(filename)) {
                std::cout << "Konfigurasi berhasil dimuat dari: " << filename << "\n";
                std::cout << "\nKonfigurasi saat ini:\n";
                std::cout << config.getAllParametersString();
            } else {
                std::cout << "Gagal memuat konfigurasi dari: " << filename << "\n";
                return 1;
            }
            return 0;
        }
        else if (arg == "--save" && argc > 2) {
            std::string filename = argv[2];
            if (config.saveToFile(filename)) {
                std::cout << "Konfigurasi berhasil disimpan ke: " << filename << "\n";
            } else {
                std::cout << "Gagal menyimpan konfigurasi ke: " << filename << "\n";
                return 1;
            }
            return 0;
        }
        else if (arg == "--show") {
            std::cout << config.getAllParametersString();
            return 0;
        }
        else if (arg == "--uci") {
            std::cout << "UCI Options:\n";
            std::cout << "============\n";
            std::cout << config.getUCIOptionsString();
            return 0;
        }
        else if (arg == "--reset") {
            config.resetToDefaults();
            std::cout << "Semua parameter telah direset ke nilai default.\n";
            std::cout << "\nKonfigurasi saat ini:\n";
            std::cout << config.getAllParametersString();
            return 0;
        }
        else {
            std::cout << "Argumen tidak dikenal: " << arg << "\n";
            std::cout << "Gunakan --help untuk melihat opsi yang tersedia.\n";
            return 1;
        }
    }
    
    // Jalankan interface konfigurasi interaktif
    try {
        EngineConfigUI configUI(config);
        configUI.run();
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
