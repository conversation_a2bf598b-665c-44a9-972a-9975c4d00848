#pragma once

#include "ChessEngine.h"
#include "ChessGame.h"
#include <string>
#include <vector>
#include <map>
#include <sstream>

// UCI (Universal Chess Interface) Protocol Implementation
class UCIProtocol {
private:
    ChessEngine engine_;
    ChessGame game_;
    bool isReady_;
    bool debugMode_;
    
    // UCI options
    std::map<std::string, std::string> options_;
    
public:
    UCIProtocol();
    
    // Main UCI loop
    void run();
    
    // UCI command handlers
    void handleUCI();
    void handleDebug(const std::string& value);
    void handleIsReady();
    void handleSetOption(const std::string& name, const std::string& value);
    void handleRegister();
    void handleUCINewGame();
    void handlePosition(const std::vector<std::string>& tokens);
    void handleGo(const std::vector<std::string>& tokens);
    void handleStop();
    void handlePonderHit();
    void handleQuit();
    
    // Utility functions
    std::vector<std::string> tokenize(const std::string& line);
    void sendResponse(const std::string& response);
    void sendInfo(const std::string& info);
    void sendBestMove(const Move& move);
    
    // Position parsing
    bool parsePosition(const std::string& fen);
    bool parseMoves(const std::vector<std::string>& moves);
    
    // Search parameters
    struct SearchParams {
        int depth = 0;
        int movetime = 0;
        int wtime = 0;
        int btime = 0;
        int winc = 0;
        int binc = 0;
        int movestogo = 0;
        bool infinite = false;
        bool ponder = false;
        std::vector<Move> searchmoves;
    };
    
    SearchParams parseGoCommand(const std::vector<std::string>& tokens);
    int calculateSearchTime(const SearchParams& params);
    
private:
    void initializeOptions();
    std::string toLowerCase(const std::string& str);
};
