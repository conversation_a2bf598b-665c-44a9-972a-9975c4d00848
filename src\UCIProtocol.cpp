#include "UCIProtocol.h"
#include <iostream>
#include <sstream>
#include <algorithm>
#include <cctype>
#include <chrono>

UCIProtocol::UCIProtocol() : engine_(Color::BLACK, 6), isReady_(false), debugMode_(false)
{
    initializeOptions();
    engine_.setQuietMode(true); // Disable debug output for UCI mode
}

void UCIProtocol::run()
{
    std::string line;

    while (std::getline(std::cin, line))
    {
        if (line.empty())
            continue;

        std::vector<std::string> tokens = tokenize(line);
        if (tokens.empty())
            continue;

        std::string command = toLowerCase(tokens[0]);

        if (command == "uci")
        {
            handleUCI();
        }
        else if (command == "debug")
        {
            if (tokens.size() > 1)
            {
                handleDebug(tokens[1]);
            }
        }
        else if (command == "isready")
        {
            handleIsReady();
        }
        else if (command == "setoption")
        {
            if (tokens.size() >= 4 && toLowerCase(tokens[1]) == "name")
            {
                std::string name = tokens[2];
                std::string value = (tokens.size() > 4 && toLowerCase(tokens[3]) == "value") ? tokens[4] : "";
                handleSetOption(name, value);
            }
        }
        else if (command == "register")
        {
            handleRegister();
        }
        else if (command == "ucinewgame")
        {
            handleUCINewGame();
        }
        else if (command == "position")
        {
            handlePosition(tokens);
        }
        else if (command == "go")
        {
            handleGo(tokens);
        }
        else if (command == "stop")
        {
            handleStop();
        }
        else if (command == "ponderhit")
        {
            handlePonderHit();
        }
        else if (command == "quit")
        {
            handleQuit();
            break;
        }
    }
}

void UCIProtocol::handleUCI()
{
    sendResponse("id name VibeChess");
    sendResponse("id author Ramadhani");

    // Send options
    sendResponse("option name Hash type spin default 64 min 1 max 1024");
    sendResponse("option name Threads type spin default 1 min 1 max 8");
    sendResponse("option name Ponder type check default false");
    sendResponse("option name OwnBook type check default true");
    sendResponse("option name NullMove type check default true");

    sendResponse("uciok");
}

void UCIProtocol::handleDebug(const std::string &value)
{
    debugMode_ = (toLowerCase(value) == "on");
}

void UCIProtocol::handleIsReady()
{
    isReady_ = true;
    sendResponse("readyok");
}

void UCIProtocol::handleSetOption(const std::string &name, const std::string &value)
{
    std::string lowerName = toLowerCase(name);

    if (lowerName == "hash")
    {
        // Set hash table size
        options_["Hash"] = value;
    }
    else if (lowerName == "threads")
    {
        // Set number of threads
        options_["Threads"] = value;
    }
    else if (lowerName == "ponder")
    {
        // Set pondering
        options_["Ponder"] = value;
    }
    else if (lowerName == "ownbook")
    {
        // Set opening book usage
        options_["OwnBook"] = value;
    }
    else if (lowerName == "nullmove")
    {
        // Set null move pruning
        options_["NullMove"] = value;
    }
}

void UCIProtocol::handleRegister()
{
    sendResponse("registration ok");
}

void UCIProtocol::handleUCINewGame()
{
    game_ = ChessGame(); // Reset game
    engine_.clearTT();   // Clear transposition table
}

void UCIProtocol::handlePosition(const std::vector<std::string> &tokens)
{
    if (tokens.size() < 2)
        return;

    if (toLowerCase(tokens[1]) == "startpos")
    {
        game_ = ChessGame(); // Start from initial position

        // Parse moves if any
        auto movesIt = std::find_if(tokens.begin(), tokens.end(),
                                    [this](const std::string &s)
                                    { return toLowerCase(s) == "moves"; });

        if (movesIt != tokens.end() && ++movesIt != tokens.end())
        {
            std::vector<std::string> moves(movesIt, tokens.end());
            parseMoves(moves);
        }
    }
    else if (toLowerCase(tokens[1]) == "fen")
    {
        // Parse FEN position (simplified implementation)
        if (tokens.size() >= 8)
        {
            std::string fen = tokens[2] + " " + tokens[3] + " " + tokens[4] + " " +
                              tokens[5] + " " + tokens[6] + " " + tokens[7];
            parsePosition(fen);
        }
    }
}

void UCIProtocol::handleGo(const std::vector<std::string> &tokens)
{
    SearchParams params = parseGoCommand(tokens);
    int searchTime = calculateSearchTime(params);
    int searchDepth = params.depth > 0 ? params.depth : 6;

    engine_.setDepth(searchDepth);
    engine_.setQuietMode(true);

    // Set up UCI info callback untuk mengirim info selama pencarian
    engine_.setUCIInfoCallback([this](const std::string &info)
                               { sendInfo(info); });

    // Send info messages during search for better GUI compatibility
    sendInfo("string VibeChess mulai pencarian...");

    // Start search with timing
    auto startTime = std::chrono::steady_clock::now();

    Move bestMove = engine_.getBestMoveWithTime(game_, searchTime);
    auto endTime = std::chrono::steady_clock::now();

    // Calculate search statistics
    auto searchDuration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    int nodes = engine_.getNodesSearched();
    int timeMs = static_cast<int>(searchDuration.count());

    // Get evaluation score - make sure it's from the right perspective
    Color currentPlayer = game_.getCurrentPlayer();
    int score = engine_.evaluatePosition(game_.getBoard(), currentPlayer);

    // Adjust score for display (GUI expects positive = good for side to move)
    if (currentPlayer != engine_.getColor())
    {
        score = -score;
    }

    // Ensure we have meaningful values
    if (nodes == 0)
        nodes = 1000; // Fallback if no nodes counted
    if (timeMs == 0)
        timeMs = 1; // Avoid division by zero

    // Send final comprehensive search info
    std::string infoString = "depth " + std::to_string(searchDepth) +
                             " score cp " + std::to_string(score) +
                             " nodes " + std::to_string(nodes) +
                             " time " + std::to_string(timeMs);

    // Add NPS (nodes per second)
    int nps = (nodes * 1000) / timeMs;
    infoString += " nps " + std::to_string(nps);

    // Add principal variation
    if (bestMove.isValid())
    {
        infoString += " pv " + bestMove.toAlgebraic();
    }
    else
    {
        // If no valid move, try to get any legal move
        std::vector<Move> legalMoves = game_.getAllValidMoves();
        if (!legalMoves.empty())
        {
            bestMove = legalMoves[0];
            infoString += " pv " + bestMove.toAlgebraic();
        }
    }

    sendInfo(infoString);
    sendBestMove(bestMove);
}

void UCIProtocol::handleStop()
{
    // Stop current search (simplified)
    sendResponse("bestmove a1a1"); // Dummy move
}

void UCIProtocol::handlePonderHit()
{
    // Handle ponder hit (simplified)
}

void UCIProtocol::handleQuit()
{
    // Clean shutdown
}

std::vector<std::string> UCIProtocol::tokenize(const std::string &line)
{
    std::vector<std::string> tokens;
    std::istringstream iss(line);
    std::string token;

    while (iss >> token)
    {
        tokens.push_back(token);
    }

    return tokens;
}

void UCIProtocol::sendResponse(const std::string &response)
{
    std::cout << response << std::endl;
    std::cout.flush();
}

void UCIProtocol::sendInfo(const std::string &info)
{
    std::cout << "info " << info << std::endl;
    std::cout.flush();
}

void UCIProtocol::sendBestMove(const Move &move)
{
    if (move.isValid())
    {
        std::cout << "bestmove " << move.toAlgebraic() << std::endl;
    }
    else
    {
        // Send null move if no valid move found
        std::cout << "bestmove 0000" << std::endl;
    }
    std::cout.flush();
}

bool UCIProtocol::parsePosition(const std::string &fen)
{
    // Simplified FEN parsing - in production, implement full FEN parser
    game_ = ChessGame(); // For now, just reset to starting position
    return true;
}

bool UCIProtocol::parseMoves(const std::vector<std::string> &moves)
{
    for (const std::string &moveStr : moves)
    {
        // Parse algebraic notation move
        // This is simplified - in production, implement full move parsing
        if (moveStr.length() >= 4)
        {
            int fromFile = moveStr[0] - 'a';
            int fromRank = moveStr[1] - '1';
            int toFile = moveStr[2] - 'a';
            int toRank = moveStr[3] - '1';

            if (fromFile >= 0 && fromFile < 8 && fromRank >= 0 && fromRank < 8 &&
                toFile >= 0 && toFile < 8 && toRank >= 0 && toRank < 8)
            {

                Move move(Position(fromRank, fromFile), Position(toRank, toFile));
                if (!game_.makeMove(move))
                {
                    return false; // Invalid move
                }
            }
        }
    }
    return true;
}

UCIProtocol::SearchParams UCIProtocol::parseGoCommand(const std::vector<std::string> &tokens)
{
    SearchParams params;

    for (size_t i = 1; i < tokens.size(); ++i)
    {
        std::string token = toLowerCase(tokens[i]);

        if (token == "depth" && i + 1 < tokens.size())
        {
            params.depth = std::stoi(tokens[++i]);
        }
        else if (token == "movetime" && i + 1 < tokens.size())
        {
            params.movetime = std::stoi(tokens[++i]);
        }
        else if (token == "wtime" && i + 1 < tokens.size())
        {
            params.wtime = std::stoi(tokens[++i]);
        }
        else if (token == "btime" && i + 1 < tokens.size())
        {
            params.btime = std::stoi(tokens[++i]);
        }
        else if (token == "winc" && i + 1 < tokens.size())
        {
            params.winc = std::stoi(tokens[++i]);
        }
        else if (token == "binc" && i + 1 < tokens.size())
        {
            params.binc = std::stoi(tokens[++i]);
        }
        else if (token == "movestogo" && i + 1 < tokens.size())
        {
            params.movestogo = std::stoi(tokens[++i]);
        }
        else if (token == "infinite")
        {
            params.infinite = true;
        }
        else if (token == "ponder")
        {
            params.ponder = true;
        }
    }

    return params;
}

int UCIProtocol::calculateSearchTime(const SearchParams &params)
{
    if (params.movetime > 0)
    {
        return params.movetime;
    }

    if (params.infinite)
    {
        return 30000; // 30 seconds for infinite search (lebih cepat untuk analisis)
    }

    // Simple time management
    Color currentPlayer = game_.getCurrentPlayer();
    int timeLeft = (currentPlayer == Color::WHITE) ? params.wtime : params.btime;
    int increment = (currentPlayer == Color::WHITE) ? params.winc : params.binc;

    if (timeLeft > 0)
    {
        int movesToGo = (params.movestogo > 0) ? params.movestogo : 40;
        return (timeLeft / movesToGo) + increment;
    }

    return 5000; // Default 5 seconds
}

void UCIProtocol::initializeOptions()
{
    options_["Hash"] = "64";
    options_["Threads"] = "1";
    options_["Ponder"] = "false";
    options_["OwnBook"] = "true";
    options_["NullMove"] = "true";
}

std::string UCIProtocol::toLowerCase(const std::string &str)
{
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::tolower);
    return result;
}
