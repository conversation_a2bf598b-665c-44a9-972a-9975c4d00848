# Chess Engine

A complete chess engine implementation in C++ featuring a console-based interface and AI opponent using minimax algorithm with alpha-beta pruning.

## Features

### Core Chess Functionality
- **Complete rule implementation**: All standard chess rules including castling, en passant, and pawn promotion
- **Move validation**: Comprehensive move validation system ensuring only legal moves are allowed
- **Game state detection**: Automatic detection of check, checkmate, stalemate, and draw conditions
- **Move history**: Complete game history tracking with algebraic notation

### AI Engine
- **Minimax algorithm**: Advanced AI using minimax with alpha-beta pruning for efficient search
- **Position evaluation**: Sophisticated evaluation function considering:
  - Material values (piece worth)
  - Piece-square tables for positional play
  - Endgame vs middlegame evaluation
  - King safety and piece activity
- **Configurable depth**: Adjustable search depth (default: 4 moves ahead)
- **Move ordering**: Optimized move ordering for better alpha-beta pruning efficiency

### User Interface
- **Console-based interface**: Clean, intuitive text-based interface
- **Algebraic notation**: Standard chess notation for move input (e.g., "e2e4")
- **Visual board display**: ASCII art board representation with piece symbols
- **Interactive commands**: Help system, board display, game status, and more
- **Error handling**: Comprehensive input validation and error messages

## Project Structure

```
Chess-Engine/
├── include/           # Header files
│   ├── ChessTypes.h   # Basic types and enums
│   ├── Piece.h        # Base piece class
│   ├── Pieces.h       # Specific piece implementations
│   ├── ChessBoard.h   # Board representation
│   ├── Move.h         # Move class
│   ├── ChessGame.h    # Game logic
│   ├── ChessEngine.h  # AI engine
│   └── ChessUI.h      # User interface
├── src/               # Implementation files
│   ├── Piece.cpp
│   ├── Pieces.cpp
│   ├── ChessBoard.cpp
│   ├── ChessGame.cpp
│   ├── ChessEngine.cpp
│   └── ChessUI.cpp
├── main.cpp           # Main application
├── CMakeLists.txt     # Build configuration
└── README.md          # This file
```

## Building the Project

### Prerequisites
- C++17 compatible compiler (GCC 7+, Clang 5+, MSVC 2017+)
- CMake 3.10 or higher

### Build Instructions

#### Using CMake (Recommended)
```bash
# Create build directory
mkdir build
cd build

# Configure the project
cmake ..

# Build the project
cmake --build .

# Run the executable
./chess_engine        # Linux/Mac
chess_engine.exe      # Windows
```

#### Manual Compilation
```bash
# Compile all source files
g++ -std=c++17 -Iinclude -O2 main.cpp src/*.cpp -o chess_engine
```

## How to Play

### Starting the Game
1. Run the executable
2. Choose your color (White or Black)
3. The game begins with the standard chess starting position

### Making Moves
- Use algebraic notation: `e2e4` (move piece from e2 to e4)
- Special moves:
  - **Castling**: Move king two squares (`e1g1` for kingside, `e1c1` for queenside)
  - **En passant**: Capture the pawn that just moved two squares
  - **Pawn promotion**: Add piece letter at end (`e7e8q` for queen promotion)
    - `q` = Queen, `r` = Rook, `b` = Bishop, `n` = Knight

### Commands
- `help` or `h` - Show help information
- `board` or `b` - Display current board
- `status` or `s` - Show game status
- `quit` or `q` - Exit the game

### Example Game Session
```
========================================
         WELCOME TO CHESS ENGINE       
========================================

Do you want to play as White or Black? (w/b): w

Game setup complete!
You are playing as White
Enter moves in algebraic notation (e.g., 'e2e4')
Type 'help' for more commands.

  +---+---+---+---+---+---+---+---+
8 | r | n | b | q | k | b | n | r |
  +---+---+---+---+---+---+---+---+
7 | p | p | p | p | p | p | p | p |
  +---+---+---+---+---+---+---+---+
6 |   |   |   |   |   |   |   |   |
  +---+---+---+---+---+---+---+---+
5 |   |   |   |   |   |   |   |   |
  +---+---+---+---+---+---+---+---+
4 |   |   |   |   |   |   |   |   |
  +---+---+---+---+---+---+---+---+
3 |   |   |   |   |   |   |   |   |
  +---+---+---+---+---+---+---+---+
2 | P | P | P | P | P | P | P | P |
  +---+---+---+---+---+---+---+---+
1 | R | N | B | Q | K | B | N | R |
  +---+---+---+---+---+---+---+---+
    a   b   c   d   e   f   g   h

Current player: White
Game in progress

White to move: e2e4
Move e2e4 played successfully!

Computer is thinking...
Computer plays: e7e5
```

## Technical Details

### Architecture
The chess engine follows object-oriented design principles with clear separation of concerns:

- **ChessTypes.h**: Defines core types, enums, and utility functions
- **Piece hierarchy**: Abstract base class with specific implementations for each piece type
- **ChessBoard**: Manages the 8x8 board state and piece positions
- **Move**: Represents chess moves with support for special move types
- **ChessGame**: Handles game logic, turn management, and rule enforcement
- **ChessEngine**: AI implementation with minimax and position evaluation
- **ChessUI**: User interface and input/output handling

### AI Algorithm
The AI uses a minimax algorithm with alpha-beta pruning:
1. **Search depth**: Configurable depth (default 4 moves)
2. **Position evaluation**: Combines material value and positional factors
3. **Move ordering**: Prioritizes captures and promising moves
4. **Alpha-beta pruning**: Eliminates unnecessary search branches

### Performance
- **Search efficiency**: Alpha-beta pruning reduces search space significantly
- **Move generation**: Optimized move generation for each piece type
- **Memory management**: Smart pointers for automatic memory management
- **Compilation**: Optimized builds with -O2 flag for better performance

## Contributing

This is a complete, self-contained chess engine implementation. The code is well-documented and follows modern C++ best practices.

## License

This project is provided as-is for educational and personal use.
